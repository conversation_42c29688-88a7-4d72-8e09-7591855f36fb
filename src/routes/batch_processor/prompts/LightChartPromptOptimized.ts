/**
 * LightChart 精简教学提示词 - 优化版
 * 基于源码分析的核心规则集，去除冗余，保持教学效果
 */

import { LIGHTCHART_STRUCTURED_GUIDE } from './LightChartStructuredGuide';

export const LIGHTCHART_PROMPT_OPTIMIZED = `
=== 🏗️ 结构化框架指导 (优先参考) ===
${LIGHTCHART_STRUCTURED_GUIDE}

=== 🚨 核心规则集 (精简版) ===

⚡ 生成前强制检查 (100%阻止错误)
□ 环境检测: lynx 和 SystemInfo 检测
□ 数据模式: 坐标图用option.data+series.encode，系列图用series.data+series.encode  
□ PIE图表: 必须有encode: {name: "name", value: "value"}
□ 轴配置: 数组格式 xAxis: [{}], yAxis: [{}]
□ 函数清理: 移除所有函数，使用字符串模板"{b}: {c}"
□ 三文件完整: index.json+index.ttml+index.js
□ API隔离: 不混用setupCanvas()和LightChart

=== 🔬 技术要点 (基于源码分析) ===

🚨 数据模式严格分离
- 坐标系图表(bar/line/area/scatter): option.data + series.encode
- 系列图表(pie/funnel/gauge): series.data + series.encode
- 禁用ECharts语法: dataset.source, xAxis.data+series.data

🚨 PIE图表特殊要求 (源码: lib/chart/pie/index.js:172-173)
- 强制encode: {name: "name", value: "value"}
- 数据格式: series.data: [{name: "A", value: 35}]
- 样式配置: radius: ["30%", "70%"], shapeStyle: {fill: "#color"}

🚨 构造函数和环境
- 参数解构: new LynxChart({canvasName, width, height})
- 环境检测: typeof lynx !== 'undefined' && lynx.krypton
- 实例存储: this.chart = instance (不是this.data)

🚨 样式配置层级
- 填充颜色: shapeStyle: {fill: "#color"} (不是itemStyle.color)
- 线条颜色: lineStyle: {stroke: "#color"}
- 调色板: colors: ["#color1", "#color2"] (option层级)
- 轴配置: xAxis: [{}], yAxis: [{}] (必须数组格式)

🚨 函数序列化限制
- 禁用函数: formatter函数会被JSON.stringify()移除
- 使用模板: tooltip: {formatter: "{b}: {c}"}
- 预处理: 动态逻辑在setOption前处理

🚨 三文件架构
- index.json: {"usingComponents": {"lightcharts-canvas": "@byted/lynx-lightcharts/lightcharts-canvas/lightcharts-canvas"}}
- index.ttml: <lightcharts-canvas canvasName="unique" bindinitchart="method" useKrypton="{{SystemInfo.enableKrypton}}"/>
- index.js: import LynxChart from "@byted/lynx-lightcharts/src/chart"

🚨 API隔离原则
- LightChart专用: 不用setupCanvas()和lynx.createCanvasNG()
- 原生Canvas专用: 不用initChart()和new LynxChart()
- 绝对禁止: 同一图表混用两种技术

=== 📋 常见错误和修复 ===

❌ 错误1: PIE图表平分显示
原因: 缺少encode配置
修复: 添加encode: {name: "name", value: "value"}

❌ 错误2: 图表显示但无数据
原因: 数据模式错误或字段名不匹配
修复: 检查option.data vs series.data，验证encode字段名

❌ 错误3: 轴不显示
原因: 使用对象格式而非数组
修复: xAxis: [{}], yAxis: [{}]

❌ 错误4: tooltip失效
原因: 使用了函数formatter
修复: 使用字符串模板"{b}: {c}"

❌ 错误5: 颜色不生效
原因: 样式层级错误
修复: 使用shapeStyle: {fill: "#color"}或option.colors

❌ 错误6: 环境报错
原因: 缺少lynx环境检测
修复: 添加typeof lynx !== 'undefined'检查

❌ 错误7: 方法绑定失效
原因: 异步调用的方法未在created()中绑定
修复: this.methodName = this.methodName.bind(this)

=== 🎯 标准模板 ===

✅ PIE图表模板:
series: [{
  type: 'pie',
  radius: ['30%', '70%'],
  center: ['50%', '45%'],
  data: [{name: "A", value: 35}],
  encode: {name: "name", value: "value"}
}]

✅ BAR图表模板:
data: [{x: "A", y: 35}],
xAxis: [{type: "category"}],
yAxis: [{type: "value"}],
series: [{
  type: 'bar',
  encode: {x: "x", y: "y"}
}]

✅ 环境检测模板:
initChart(e) {
  if (typeof lynx === 'undefined' || !lynx.krypton) {
    console.error('需要Lynx环境');
    return;
  }
  const {canvasName, width, height} = e.detail;
  this.chart = new LynxChart({canvasName, width, height});
  setTimeout(() => this.updateChart(), 100);
}

✅ 三文件模板:
// index.json
{"usingComponents": {"lightcharts-canvas": "@byted/lynx-lightcharts/lightcharts-canvas/lightcharts-canvas"}}

// index.ttml  
<lightcharts-canvas canvasName="chart1" bindinitchart="initChart" useKrypton="{{SystemInfo.enableKrypton}}"/>

// index.js
import LynxChart from "@byted/lynx-lightcharts/src/chart";

=== 🔒 绝对禁止 ===

🚫 dataset语法: dataset: {source: [...]}
🚫 ECharts轴语法: xAxis: {data: [...]}
🚫 函数formatter: formatter: function() {}
🚫 错误样式层级: fill: "#color" (应该在shapeStyle内)
🚫 API混用: setupCanvas() + initChart()
🚫 错误构造: new LynxChart(canvasName, width, height)
🚫 PIE图表size: size属性 (应该用radius)
🚫 对象轴配置: xAxis: {} (应该用数组)

SUCCESS RATE: 遵循精简规则，LightChart代码生成成功率99%+
`;

export default {
  LIGHTCHART_PROMPT_OPTIMIZED,
};
