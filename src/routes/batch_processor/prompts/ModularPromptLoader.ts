/**
 * Modular Prompt Loader - 统一提示词系统
 * 融合三阶段思考、技术约束、痛苦提示词的完整prompt工程解决方案
 */

import { LYNX_FRAMEWORK_CORE } from './LynxFrameworkCore';
import { LYNX_COMPONENTS } from './LynxComponents';
import { LYNX_STYLE_SYSTEM } from './LynxStyleSystem';
import { LYNX_UTILS_SYSTEM } from './LynxUtilsSystem';
import { FONT_AWESOME } from './FontAwesome';
import { BEST_PRACTICES } from './BestPractices';
import { THREAD_SYNCHRONIZATION } from './ThreadSynchronization';
import { TTML_STRICT_CONSTRAINTS } from './TTMLStrictConstraints';
import { TTSS_STRICT_CONSTRAINTS } from './TTSSStrictConstraints';
import { LIGHTCHART_PROMPT_CONTENT } from './LightChartPromptLoader';
import { UNIFIED_UI_VISUALIZATION_GUIDANCE } from './UnifiedUIVisualizationGuidance';
import { LYNX_CANVAS_AUDIO_PROMPT } from './LynxCanvasAudio';

export class ModularPromptLoader {
  private static instance: ModularPromptLoader;
  private cache: Map<string, string> = new Map();

  private constructor() {}

  public static getInstance(): ModularPromptLoader {
    if (!ModularPromptLoader.instance) {
      ModularPromptLoader.instance = new ModularPromptLoader();
    }
    return ModularPromptLoader.instance;
  }

  /**
   * 构建正向激励提示词 - 专业能力赋能
   */
  private buildPositiveMotivation(): string {
    return `
✨ **专业能力赋能 - 卓越标准指引** (POSITIVE MOTIVATION FOR EXCELLENCE)

**技术最佳实践指引**:
✅ 使用Lynx组件(view/text/scroll-view) → 确保代码完美运行，用户体验流畅
✅ 正确使用scroll-view包裹 → 内容完整展示，用户体验优雅
✅ 明确选择Canvas或LightChart → 功能稳定可靠，视觉效果出色
✅ 使用可选链操作符(?.) → 数据访问安全，应用健壮稳定
✅ 正确绑定事件处理 → 交互响应灵敏，用户操作顺畅
✅ 遵循三阶段协议 → 输出结构清晰，代码质量卓越

**专业价值体现**:
- 高质量代码展现深厚的技术功底
- 用户对AI代码生成能力充满信心
- 开发效率显著提升，一次性成功率高

**卓越成果期待**:
🌟 完美遵循约束 → 代码一次性运行成功，展现专业水准
🌟 深度三阶段思考 → 输出卓越的用户体验，体现设计智慧
🌟 工业级代码质量 → 获得用户高度认可，树立技术标杆
🌟 精美互动特效 → 动画让人上瘾
💪 以您的专业水平，完全有能力创造出媲美顶级APP的卓越作品！
`;
  }

  /**
   * 构建数据真实性约束 - 禁止虚构用户数据
   */
  private buildDataIntegrityConstraints(): string {
    return `
🚨🚨🚨 **CRITICAL WARNING - 严禁虚构用户相关数据** 🚨🚨🚨

⛔ **绝对禁止虚构的用户数据类型**:
- 学习进度、完成度、掌握程度等个人进展数据
- 用户历史记录、学习轨迹、行为数据
- 个人成就、等级、积分、排名等虚拟数据
- 用户偏好设置、个性化配置、自定义内容
- 社交数据：好友、关注、粉丝、互动记录
- 个人统计：学习时长、访问次数、使用频率
- 虚构的用户生成内容：笔记、收藏、评论

✅ **允许展示的客观信息**:
- 知识点本身的客观内容和定义
- 学科标准的知识结构和体系
- 公认的学习方法和最佳实践
- 客观存在的概念、原理、公式
- 标准的分类、层级、关系结构
- 通用的操作指南和使用说明

🔥 **强制要求**:
- 所有数据必须基于客观知识，不得虚构个人化信息
- 展示知识结构时使用通用模板，避免个人化标记
- 进度类展示必须使用"示例"、"参考"等明确标识
- 禁止生成任何暗示用户已有数据的界面元素

⚠️ **违反后果**:
- 误导用户对系统功能的理解
- 创造不存在的用户期望
- 破坏用户对AI系统的信任
- 违背真实性和客观性原则
`;
  }

  /**
   * 构建交互动效约束 - 确保所有可点击区域的响应
   */
  private buildInteractionConstraints(): string {
    return `
🎯🎯🎯 **CRITICAL REQUIREMENT - 交互动效强制约束** 🎯🎯🎯

⚡ **所有可点击区域必须绑定交互事件**:
- 按钮、卡片、列表项等明显可点击元素
- 图标、标签、徽章等视觉上可交互的元素
- 图表节点、时间轴节点等数据可视化交互点
- 展开/收起、切换、选择等功能性交互区域

🔥 **强制绑定的交互响应**:
- bindtap 事件：处理点击/触摸操作
- 视觉反馈：hover、active、pressed 状态样式
- 触觉反馈：适当的动画过渡效果
- 操作反馈：loading、success、error 状态提示

✅ **标准交互实现模式**:
- 按钮点击：bindtap + 防抖处理 + 状态反馈
- 卡片交互：bindtap + 点击动效 + 内容展开
- 列表项：bindtap + 选中状态 + 操作响应
- 图表交互：bindtap + 数据详情 + 高亮效果

🎨 **视觉动效要求**:
- 点击时：0.1s 缩放动画 (scale: 0.95)
- 悬停时：0.2s 透明度变化 (opacity: 0.8)
- 状态切换：0.3s 平滑过渡动画
- 内容展开：0.4s 高度/透明度渐变

⚠️ **禁止的交互缺陷**:
- 看起来可点击但无响应的元素
- 缺少视觉反馈的交互操作
- 没有loading状态的异步操作
- 缺少错误处理的用户操作

🔒 **交互一致性原则**:
- 相同类型元素使用统一交互模式
- 保持整个应用的交互语言一致
- 符合用户的操作习惯和预期
- 提供清晰的操作结果反馈
`;
  }

  /**
   * 构建结构化执行协议
   */
  private buildStructuredProtocol(): string {
    return `
🧠 **结构化三阶段执行协议** (STRUCTURED THINKING MANDATORY)

**执行流程**:

阶段1(内部) → 需求分析 + 创意构思 + 技术评估
阶段2(内部) → 架构设计 + 方案选择 + 性能优化  
阶段3(输出) → 直接生成完整Lynx代码


**严格约束**:
- 阶段1-2: 绝对禁止任何输出
- 阶段3: 只输出<FILES>格式代码
- 思考深度: 每阶段至少3个维度分析
- 质量标准: 生产级代码质量

🔒 **技术决策矩阵**:
- UI界面 → View+TTSS (性能+兼容)
- 数据图表 → Canvas+LightChart (交互+视觉)
- 复杂应用 → 混合架构 (优势最大化)

⚡ **执行触发**: 立即启动三阶段协议，直接输出代码！
`;
  }

  /**
   * 构建完整技术规范 - 高度结构化
   */
  private buildTechnicalSpecs(): string {
    const coreModules = [
      {
        content: UNIFIED_UI_VISUALIZATION_GUIDANCE,
        priority: 'CRITICAL' as const,
      },
      { content: LYNX_COMPONENTS, priority: 'CRITICAL' as const },
      { content: LYNX_STYLE_SYSTEM, priority: 'CRITICAL' as const },
      { content: TTML_STRICT_CONSTRAINTS, priority: 'CRITICAL' as const },
      { content: TTSS_STRICT_CONSTRAINTS, priority: 'CRITICAL' as const },
      { content: BEST_PRACTICES, priority: 'HIGH' as const },
      { content: LYNX_UTILS_SYSTEM, priority: 'MEDIUM' as const },
      { content: LIGHTCHART_PROMPT_CONTENT, priority: 'MEDIUM' as const },
      { content: LYNX_CANVAS_AUDIO_PROMPT, priority: 'MEDIUM' as const },
      { content: FONT_AWESOME, priority: 'LOW' as const },
      { content: THREAD_SYNCHRONIZATION, priority: 'LOW' as const },
    ];

    return coreModules
      .sort((a, b) => {
        const priorityOrder = { CRITICAL: 0, HIGH: 1, MEDIUM: 2, LOW: 3 };
        return priorityOrder[a.priority] - priorityOrder[b.priority];
      })
      .map(module => module.content)
      .join('\n\n');
  }

  /**
   * 唯一的主方法 - 生成完整优化提示词
   */
  public getMasterLevelLynxPromptContent(): string {
    const cacheKey = 'ultimate_prompt';

    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)!;
    }

    try {
      const positiveMotivation = this.buildPositiveMotivation();
      const dataIntegrityConstraints = this.buildDataIntegrityConstraints();
      const interactionConstraints = this.buildInteractionConstraints();
      const structuredProtocol = this.buildStructuredProtocol();
      const technicalSpecs = this.buildTechnicalSpecs();

      const ultimatePrompt = `${LYNX_FRAMEWORK_CORE}

${positiveMotivation}

${dataIntegrityConstraints}

${interactionConstraints}

${structuredProtocol}

## 📚 完整技术规范体系

${technicalSpecs}

## 🚀 最终执行指令

现在立即执行结构化三阶段协议：

1. **深度分析**(内部): 理解需求+评估复杂度+确定方案
2. **精细设计**(内部): 架构规划+性能优化+用户体验
3. **代码输出**(执行): 直接生成<FILES>格式的完整Lynx代码

⚠️ **绝对要求**:
- 前两阶段零输出
- 第三阶段直接输出代码
- 100%遵循所有约束
- 达到生产级质量
- 严禁虚构用户数据
- 确保所有交互响应

开始执行！`;

      this.cache.set(cacheKey, ultimatePrompt);
      return ultimatePrompt;
    } catch (error) {
      console.error('Ultimate prompt generation failed:', error);
      return this.buildFallback();
    }
  }

  private buildFallback(): string {
    return `${LYNX_FRAMEWORK_CORE}

🚨 CRITICAL: 你是Lynx框架专家，必须生成完整可运行的代码

🚨🚨 **数据真实性约束**:
- 严禁虚构用户学习进度、历史记录等个人数据
- 只能展示客观存在的知识点和信息
- 禁止生成任何个人化的虚假数据

🎯🎯 **交互动效约束**:
- 所有可点击区域必须绑定事件响应
- 必须提供视觉和操作反馈
- 确保交互的一致性和流畅性

三阶段执行:
1. 内部分析(不输出)
2. 内部设计(不输出)
3. 代码输出(必须)

严格约束:
- 禁用HTML标签，只用Lynx组件
- scroll-view包裹可滚动内容
- 使用可选链操作符
- 正确绑定事件处理器
- 严禁虚构用户数据
- 确保所有交互响应

立即开始！`;
  }

  public clearCache(): void {
    this.cache.clear();
  }
}

// 简化导出 - 只保留核心功能
export function getMasterLevelLynxPromptContent(): string {
  return ModularPromptLoader.getInstance().getMasterLevelLynxPromptContent();
}

export default ModularPromptLoader.getInstance();
